.background-container {
    background-size: cover;
    background-repeat: no-repeat;
    background-color: #003FCA;
    height: 28.5em;
   /* margin-top: -2.125rem;*/
}
    
.backgroundImage {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex-shrink: 0;
    background-repeat: no-repeat;
    background-position: -42% 45%;
    background-size: 784px;
    width: 84.0625rem;
    height: 23.125rem;
    padding: 0 2.5rem;
}

.hero-title {
    width: 80rem;
    margin-top: 35px;
    height: 2.25rem;
    top: 8.4375rem;
    margin-left: 1.5625rem;
    font-size: 2rem;
    font-weight: 500;
    font-size: 1.75rem;
    color:white;
}

.hero-subtitle {
    font-size: 1rem;
    margin-top: 35px;
    color:white;
    width: 57rem;
    height: 2.875rem;
    margin-left: 1.5625rem;
    top: 12.1875rem;
}

.box-shadow {
  box-shadow: none !important;
  background: none !important;
  border: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.product-nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    margin: 1.5rem 0 0 0;
}

.product-nav-breadcrumb-container {
    width: fit-content;
}

.search-section {
    height: 48px;
    width: 100%;
}

.search-alignment {
    height: 45px;
    display: flex;
    justify-content: flex-end;
}

.search-input {
    display: flex;
    align-items: center;
   
}

.product-nav-input {
    width: 46.625rem;
    border: 1px solid #5c6c8042;
    border-radius: 10px;
    height: 3rem;
    top:277px;
    left:119px;
    gap: 4px ;
}

.product-nav-input::placeholder {
    color: #5c6c80;
}


/* Left panel (optional for other filters) */
.product-catalogue-section-one {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  padding: 2rem;
}

.product-filter-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 350px;
  height: 100%;
  position: relative;
}
 
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* MODAL WINDOW */
.modal {
    position: absolute;
    background: white;
    width: 400px;
    border-radius: 8px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.3);
    transition: transform 0.2s ease;
    z-index: 1000;
    user-select: none;
}

/* HEADER */
.draggable-header {
    background: #f3f3f3;
    padding: 0.8rem 1rem;
    border-bottom: 1px solid #ddd;
    cursor: move;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* CLOSE BUTTON */
.close-button {
    background: transparent;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
}

/* MODAL BODY */
.modal-body {
    padding: 1rem;
    max-height: 60vh;
    overflow-y: auto;
}

/* Category Checkbox List */
.product-filter-category-item {
    display: block;
    margin: 0.5rem 0;
    font-size: 1rem;
}

.product-filter-category-item input {
    margin-right: 0.5rem;
    transform: scale(1.2);
    cursor: pointer;
}

/* Optional loader section */
.product-catalogue-gallery-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
}

.search-filter-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2.5rem;
    padding: 0.5rem;
}

.filter-button {
    border: 1px solid #dcdcdc;
    padding: 0.5rem 1rem;
    font-weight: bold;
     background-color: #003FCA;
     color:white;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-radius: .625rem;
    cursor: pointer;
    width: 126px;
    height: 48px;
}
.filter-icon {
    filter: brightness(0) saturate(100%) invert(100%);
}
.custom-draggable-modal {
    position: fixed;
    z-index: 9999;
}