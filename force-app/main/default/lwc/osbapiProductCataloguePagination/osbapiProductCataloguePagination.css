/* Base container styles */
.body,
.pagination__container,
.solutionWrapper {
  width: 100%;
}
.solutionShowcase__items {
  margin-top: -100px; /* move upward into blue area */
  position: relative;
  z-index: 1;
}
/* GRID LAYOUT: Product Cards */
.solutionsItem {
  display: grid;
  grid-template-columns: repeat(4, 308px);
  gap: 1rem;
  justify-content: start; /* align all tiles to the left */
  width: 100%;
  box-sizing: border-box;
}


/* Product Card Styling */
.sb-api-product {
  width: 308px;
  height: 256px;
  border: 1px solid #ccc;
  border-radius: 12px;
  padding: 24px 16px 16px 16px;
  box-sizing: border-box;
  background-color: #fff;
}


.sb-api-product:hover {
  transform: translateY(-5px);
}

/* PAGINATION STYLING */
.navigation-container {
  width: max-content;
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin: auto;
  align-items: center;
  font-family: var(--FONT-FAMILY);
}

.Navigationbutton {
  width: 2.8125rem;
  text-align: center;
  display: flex;
  flex-direction: row;
  column-gap: 0.625rem;
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
}

.NavigationPages {
  width: 9.375rem;
  text-align: center;
  display: flex;
  flex-direction: row;
  column-gap: 1.125rem;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
}

.pageSelection {
  height: 1.875rem;
  width: 1.875rem;
  color: #0a2240;
  font-weight: 200;
  font-size: 1.166875rem;
  line-height: 1.5625rem;
  padding: 0.125rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pageSelectionCurrent {
  color: #eee;
  background: #0033aa;
  border-radius: 50%;
}

/* Chevron icon styles */
.workingLeftChevron {
  filter: invert(52%) sepia(63%) saturate(7072%) hue-rotate(193deg) brightness(105%) contrast(105%);
}

.disabledRightChevron {
  filter: invert(100%) sepia(97%) saturate(13%) hue-rotate(249deg) brightness(104%) contrast(104%);
}

.ms-icn_chevron_left,
.ms-icn_chevron_right {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* Disabled styles */
.LeftButtonDisabled,
.rightButtonSecDisabled {
  pointer-events: none;
  cursor: default;
}

/* Optional tweaks for smaller devices */
@media only screen and (max-width: 500px) {
  .NavigationPages {
    width: 7.8125rem;
  }
}

.card-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  padding: 1rem;
  box-sizing: border-box;
}